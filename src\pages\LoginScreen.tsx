import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import toast from "react-hot-toast";
import { STRIPE_BACKEND_URL, getStripePublishableKey } from "../utils/env";
import { fetchJson } from "../utils/http";
import {
  GoogleAuthProvider,
  signInWithPopup,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut,
} from "firebase/auth";
import { auth } from "../firebase";
import { getFunctions, httpsCallable } from "firebase/functions";
import { ThemeToggle } from "../components/ui/ThemeToggle";
import Modal from "../components/ui/Modal";
import { loadStripe } from '@stripe/stripe-js';

// Initialize Stripe with environment variable
let stripePromise: Promise<any> | null = null;

function getStripePromise() {
  if (!stripePromise) {
    try {
      const publishableKey = getStripePublishableKey();
      stripePromise = loadStripe(publishableKey);
    } catch (error) {
      console.error('Failed to initialize Stripe:', error);
      toast.error('Stripe não está configurado corretamente');
      return null;
    }
  }
  return stripePromise;
}

interface LoginProps {
  minimal?: boolean;
  onGuestContinue?: () => void;
  onLoginSuccess?: () => void;
}

export function LoginScreen({ minimal = false, onGuestContinue, onLoginSuccess }: LoginProps) {
  const navigate = useNavigate();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [isRegistering, setIsRegistering] = useState(false);

  // State for modal flow
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
  const [selectedPriceId, setSelectedPriceId] = useState<string | null>(null);

  useEffect(() => {
    // Only redirect if on the main login page, not in a modal
    if (auth?.currentUser && !minimal) {
      navigate("/home", { replace: true });
    }
  }, [minimal, navigate]);

  const handleAuthSuccess = () => {
    localStorage.removeItem('guest_mode');
    localStorage.removeItem('ia_credits_left');
    if (onLoginSuccess) {
      onLoginSuccess();
    } else {
      navigate("/home", { replace: true });
    }
  };

  async function handleGoogle() {
    if (!auth) return toast.error("Firebase não configurado");
    try {
      setLoading(true);
      const provider = new GoogleAuthProvider();
      await signInWithPopup(auth, provider);
      toast.success("Login realizado!");
      handleAuthSuccess();
    } catch (err: any) {
      toast.error(err.message || 'Falha no login');
    } finally {
      setLoading(false);
    }
  }

  async function handleEmailPass(e: React.FormEvent) {
    e.preventDefault();
    if (!auth) return toast.error("Firebase não configurado");
    try {
      setLoading(true);
      if (isRegistering) {
        await createUserWithEmailAndPassword(auth, email, password);
        toast.success("Conta criada!");
      } else {
        await signInWithEmailAndPassword(auth, email, password);
        toast.success("Login realizado!");
      }
      handleAuthSuccess();
    } catch (err: any) {
      const code = err.code as string | undefined;
      if (code === 'auth/admin-restricted-operation') {
        toast.error('Cadastro desativado. Contate o administrador.');
      } else {
        toast.error(err.message || 'Falha no login');
      }
    } finally {
      setLoading(false);
    }
  }

  async function handleGuest() {
    localStorage.setItem("guest_mode", "1");
    if (!localStorage.getItem('ia_credits_left') || parseInt(localStorage.getItem('ia_credits_left')||'0',10)<=0) {
      localStorage.setItem('ia_credits_left', '5');
    }
    // Clean up old guest highlights (both old 'anon' and new 'guest' keys)
    Object.keys(localStorage).forEach((k) => {
      if (k.startsWith('grifos-anon-') || k.startsWith('grifos-guest-')) {
        localStorage.removeItem(k);
      }
    });
    if (auth) {
      try { await signOut(auth); } catch {}
    }
    if (!minimal) {
      navigate("/home", { replace: true });
    } else {
      onGuestContinue?.();
    }
  }

  async function startCheckout(priceId: string) {
    setLoading(true);
    try {
      const backendUrl = `${STRIPE_BACKEND_URL}/create-checkout-session`;

      const successUrl = `${window.location.origin}/home`;
      const cancelUrl = window.location.origin;

      // Chama o backend local via fetch
      const session = await fetchJson<{ id: string }>(backendUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ priceId, successUrl, cancelUrl }),
        timeout: 10000,
      });
      const sessionId = session.id;

      // Redireciona para o checkout do Stripe.
      const stripePromiseInstance = getStripePromise();
      if (!stripePromiseInstance) {
        toast.error("Stripe não está configurado corretamente.");
        return;
      }

      const stripe = await stripePromiseInstance;
      if (stripe) {
        await stripe.redirectToCheckout({ sessionId });
      } else {
        toast.error("Não foi possível carregar a página de pagamento.");
      }
    } catch (error: any) {
      console.error("Erro ao criar sessão de checkout:", error);
      const msg =
        error?.message === 'Failed to fetch'
          ? 'Não foi possível conectar ao servidor de pagamentos.'
          : error.message || 'Falha ao iniciar o processo de assinatura.';
      toast.error(msg);
    } finally {
      setLoading(false);
    }
  }

  async function handleSubscription(priceId: string) {
    if (!auth) return toast.error("Serviço de autenticação indisponível.");
    if (auth.currentUser) {
      await startCheckout(priceId);
    } else {
      setSelectedPriceId(priceId);
      setIsLoginModalOpen(true);
    }
  }

  const containerClass = minimal
    ? "flex flex-col items-center justify-center p-4 max-h-[95vh] overflow-auto"
    : "min-h-screen flex flex-col items-center justify-center p-6 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-950 dark:to-gray-900";

  // The content of the login form, to be reused in the page and the modal
  const loginFormContent = (
    <div className="w-full max-w-md mx-auto">
      <div className="modal-bg rounded-2xl shadow-2xl p-8 animate-scaleIn">
        <h2 className="text-2xl font-bold text-center mb-6">
          {isRegistering ? "Criar Conta" : "Acessar Conta"}
        </h2>

        <button
          onClick={handleGoogle}
          disabled={loading}
          className="w-full py-3 px-4 mb-4 rounded-lg bg-white text-gray-900 shadow-md flex items-center justify-center gap-3 hover:bg-gray-50 transition-colors font-medium"
        >
          <img src="https://www.gstatic.com/firebasejs/ui/2.0.0/images/auth/google.svg" alt="Google" className="w-5 h-5" />
          Entrar com Google
        </button>

        <form onSubmit={handleEmailPass} className="space-y-4">
          <input
            type="email"
            placeholder="Email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="w-full px-4 py-3 rounded-lg bg-white/5 border border-white/20 placeholder-white/60 focus:border-primary focus:outline-none transition-colors"
            required
          />
          <input
            type="password"
            placeholder="Senha"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            className="w-full px-4 py-3 rounded-lg bg-white/5 border border-white/20 placeholder-white/60 focus:border-primary focus:outline-none transition-colors"
            required
          />
          <button
            type="submit"
            disabled={loading}
            className="w-full py-3 px-4 rounded-lg bg-primary hover:bg-primary-dark transition-colors font-medium text-white"
          >
            {isRegistering ? "Registrar" : "Entrar"}
          </button>
        </form>

        <button
          onClick={() => setIsRegistering(!isRegistering)}
          className="w-full mt-4 text-sm opacity-80 hover:opacity-100 transition-opacity"
        >
          {isRegistering ? "Já tem uma conta? Entrar" : "Não tem uma conta? Registrar"}
        </button>

        <div className="text-center mt-6">
          <button
            onClick={handleGuest}
            className="text-sm opacity-80 hover:opacity-100 underline transition-opacity"
          >
            Continuar como Convidado
          </button>
        </div>
      </div>
    </div>
  );

  if (minimal) {
    return (
      <div className="max-h-[80vh] overflow-y-auto">
        {loginFormContent}

        {/* Plans Section for Modal */}
        <div className="mt-8 pt-6 border-t border-white/10">
          <h3 className="text-xl font-bold text-center mb-6">Nossos Planos</h3>

          <div className="space-y-4">
            {/* Plan 1 */}
            <div className="border border-white/20 rounded-lg p-4">
              <div className="text-center mb-3">
                <h4 className="text-lg font-bold">PLANO 1</h4>
                <p className="text-2xl font-semibold">
                  R$9,99<span className="text-sm font-normal opacity-80">/MÊS</span>
                </p>
              </div>
              <ul className="text-sm space-y-2 opacity-80 mb-4">
                <li className="flex items-center gap-2">
                  <span className="text-green-400">✓</span>
                  Grifos salvos na nuvem
                </li>
                <li className="flex items-center gap-2">
                  <span className="text-green-400">✓</span>
                  App celular offline com sincronização
                </li>
                <li className="flex items-center gap-2">
                  <span className="text-green-400">✓</span>
                  Possibilidade de API Key particular para IA
                </li>
              </ul>
              <button
                onClick={() => handleSubscription('price_1Ri1PbGaOqxUlEi69DL9vmnl')}
                className="w-full py-2 px-4 rounded-lg bg-primary hover:bg-primary-dark transition-colors font-medium text-white"
              >
                Assinar
              </button>
            </div>

            {/* Plan 2 */}
            <div className="border border-white/20 rounded-lg p-4">
              <div className="text-center mb-3">
                <h4 className="text-lg font-bold">PLANO 2</h4>
                <p className="text-2xl font-semibold">
                  R$19,99<span className="text-sm font-normal opacity-80">/MÊS</span>
                </p>
              </div>
              <ul className="text-sm space-y-2 opacity-80 mb-4">
                <li className="flex items-center gap-2">
                  <span className="text-green-400">✓</span>
                  Tudo do Plano 1
                </li>
                <li className="flex items-center gap-2">
                  <span className="text-green-400">✓</span>
                  Pacote de tokens para IAs (Gemini/GPT/Claude)
                </li>
              </ul>
              <button
                onClick={() => handleSubscription('price_1PjL8PGaOqxUlEi65sM2pYtP')}
                className="w-full py-2 px-4 rounded-lg bg-secondary hover:bg-secondary-dark transition-colors font-medium text-white"
              >
                Assinar
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={containerClass}>
      <div className="absolute top-4 right-4">
        <ThemeToggle />
      </div>
      
      {loginFormContent}

      {/* Seção de Planos de Assinatura */}
      <div className="mt-12 w-full max-w-4xl">
        <h3 className="text-2xl font-bold text-center mb-8">Nossos Planos</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Plan 1 */}
          <div className="modal-bg border border-white/20 rounded-2xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105">
            <div className="text-center mb-6">
              <h4 className="text-xl font-bold mb-2">PLANO 1</h4>
              <div className="text-3xl font-bold">
                R$9,99<span className="text-lg font-normal opacity-80">/MÊS</span>
              </div>
            </div>
            <ul className="space-y-3 mb-6">
              <li className="flex items-center gap-3 text-sm">
                <span className="text-green-400 text-lg">✓</span>
                Grifos salvos na nuvem
              </li>
              <li className="flex items-center gap-3 text-sm">
                <span className="text-green-400 text-lg">✓</span>
                App celular offline com sincronização
              </li>
              <li className="flex items-center gap-3 text-sm">
                <span className="text-green-400 text-lg">✓</span>
                Possibilidade de API Key particular para IA
              </li>
            </ul>
            <button
              onClick={() => handleSubscription('price_1Ri1PbGaOqxUlEi69DL9vmnl')}
              className="w-full py-3 px-4 rounded-lg bg-primary hover:bg-primary-dark transition-colors font-medium text-white"
            >
              Assinar
            </button>
          </div>

          {/* Plan 2 */}
          <div className="modal-bg border border-white/20 rounded-2xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105">
            <div className="text-center mb-6">
              <h4 className="text-xl font-bold mb-2">PLANO 2</h4>
              <div className="text-3xl font-bold">
                R$19,99<span className="text-lg font-normal opacity-80">/MÊS</span>
              </div>
            </div>
            <ul className="space-y-3 mb-6">
              <li className="flex items-center gap-3 text-sm">
                <span className="text-green-400 text-lg">✓</span>
                Tudo do Plano 1
              </li>
              <li className="flex items-center gap-3 text-sm">
                <span className="text-green-400 text-lg">✓</span>
                Pacote de tokens para IAs (Gemini/GPT/Claude)
              </li>
            </ul>
            <button
              onClick={() => handleSubscription('price_1PjL8PGaOqxUlEi65sM2pYtP')}
              className="w-full py-3 px-4 rounded-lg bg-secondary hover:bg-secondary-dark transition-colors font-medium text-white"
            >
              Assinar
            </button>
          </div>
        </div>
      </div>

      <Modal isOpen={isLoginModalOpen} onClose={() => setIsLoginModalOpen(false)}>
        <LoginScreen 
          minimal
          onLoginSuccess={() => {
            setIsLoginModalOpen(false);
            if (selectedPriceId) {
              startCheckout(selectedPriceId);
            }
          }}
          onGuestContinue={() => setIsLoginModalOpen(false)}
        />
      </Modal>
    </div>
  );
} 