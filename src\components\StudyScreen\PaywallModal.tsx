import React, { useState } from 'react';
import { GoogleAuthProvider, signInWithPopup, signInWithEmailAndPassword, createUserWithEmailAndPassword } from 'firebase/auth';
import { auth } from '../../firebase';
import toast from 'react-hot-toast';
import type { PaywallModalProps } from '../../types/lawView';

export const PaywallModal: React.FC<PaywallModalProps> = ({
  isVisible,
  creditsLeft,
  navigate,
  onDismiss,
}) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isRegistering, setIsRegistering] = useState(false);
  const [loading, setLoading] = useState(false);

  if (!isVisible) return null;

  const handleGoogle = async () => {
    if (!auth) return toast.error('Firebase não configurado');
    try {
      setLoading(true);
      const provider = new GoogleAuthProvider();
      await signInWithPopup(auth, provider);
      toast.success('Login realizado!');
      onDismiss();
    } catch (err: any) {
      toast.error(err.message || 'Falha no login');
    } finally {
      setLoading(false);
    }
  };

  const handleEmailPass = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!auth) return toast.error('Firebase não configurado');
    try {
      setLoading(true);
      if (isRegistering) {
        await createUserWithEmailAndPassword(auth, email, password);
        toast.success('Conta criada!');
      } else {
        await signInWithEmailAndPassword(auth, email, password);
        toast.success('Login realizado!');
      }
      onDismiss();
    } catch (err: any) {
      const code = err.code as string | undefined;
      if (code === 'auth/admin-restricted-operation') {
        toast.error('Cadastro desativado. Contate o administrador.');
      } else {
        toast.error(err.message || 'Falha no login');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleSubscription = (priceId: string) => {
    // Redireciona para a página de login com o plano selecionado
    navigate(`/login?plan=${priceId}`);
  };

  return (
    <div className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="modal-bg rounded-2xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-y-auto animate-scaleIn mx-auto">
        {/* Header */}
        <div className="p-6 text-center border-b border-white/10">
          <h2 className="text-2xl font-bold mb-2">Acessar Conta</h2>
          <p className="text-sm opacity-80">
            {creditsLeft > 0
              ? `Você tem ${creditsLeft} créditos restantes. Faça login para acesso ilimitado.`
              : 'Seus créditos acabaram. Faça login para continuar.'
            }
          </p>
        </div>

        {/* Login Form */}
        <div className="p-6 space-y-4">
          <button
            onClick={handleGoogle}
            disabled={loading}
            className="w-full py-3 px-4 rounded-lg bg-white text-gray-900 shadow-md flex items-center justify-center gap-3 hover:bg-gray-50 transition-colors font-medium"
          >
            <img
              src="https://www.gstatic.com/firebasejs/ui/2.0.0/images/auth/google.svg"
              alt="Google"
              className="w-5 h-5"
            />
            Entrar com Google
          </button>

          <form onSubmit={handleEmailPass} className="space-y-3">
            <input
              type="email"
              placeholder="Email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full px-4 py-3 rounded-lg bg-white/5 border border-white/20 placeholder-white/60 focus:border-primary focus:outline-none transition-colors"
              required
            />
            <input
              type="password"
              placeholder="Senha"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full px-4 py-3 rounded-lg bg-white/5 border border-white/20 placeholder-white/60 focus:border-primary focus:outline-none transition-colors"
              required
            />
            <button
              type="submit"
              disabled={loading}
              className="w-full py-3 px-4 rounded-lg bg-primary hover:bg-primary-dark transition-colors font-medium text-white"
            >
              {isRegistering ? 'Registrar' : 'Entrar'}
            </button>
          </form>

          <button
            onClick={() => setIsRegistering(!isRegistering)}
            className="w-full text-sm opacity-80 hover:opacity-100 transition-opacity"
          >
            {isRegistering ? 'Já tem uma conta? Entrar' : 'Não tem uma conta? Registrar'}
          </button>

          <div className="text-center">
            <button
              onClick={onDismiss}
              className="text-sm opacity-80 hover:opacity-100 underline transition-opacity"
            >
              Continuar como Convidado
            </button>
          </div>
        </div>

        {/* Plans Section */}
        <div className="p-6 border-t border-white/10">
          <h3 className="text-xl font-bold text-center mb-6">Nossos Planos</h3>

          <div className="space-y-4">
            {/* Plan 1 */}
            <div className="border border-white/20 rounded-lg p-4">
              <div className="text-center mb-3">
                <h4 className="text-lg font-bold">PLANO 1</h4>
                <p className="text-2xl font-semibold">
                  R$9,99<span className="text-sm font-normal opacity-80">/MÊS</span>
                </p>
              </div>
              <ul className="text-sm space-y-2 opacity-80 mb-4">
                <li className="flex items-center gap-2">
                  <span className="text-green-400">✓</span>
                  Grifos salvos na nuvem
                </li>
                <li className="flex items-center gap-2">
                  <span className="text-green-400">✓</span>
                  App celular offline com sincronização
                </li>
                <li className="flex items-center gap-2">
                  <span className="text-green-400">✓</span>
                  Possibilidade de API Key particular para IA
                </li>
              </ul>
              <button
                onClick={() => handleSubscription('price_1Ri1PbGaOqxUlEi69DL9vmnl')}
                className="w-full py-2 px-4 rounded-lg bg-primary hover:bg-primary-dark transition-colors font-medium text-white"
              >
                Assinar
              </button>
            </div>

            {/* Plan 2 */}
            <div className="border border-white/20 rounded-lg p-4">
              <div className="text-center mb-3">
                <h4 className="text-lg font-bold">PLANO 2</h4>
                <p className="text-2xl font-semibold">
                  R$19,99<span className="text-sm font-normal opacity-80">/MÊS</span>
                </p>
              </div>
              <ul className="text-sm space-y-2 opacity-80 mb-4">
                <li className="flex items-center gap-2">
                  <span className="text-green-400">✓</span>
                  Tudo do Plano 1
                </li>
                <li className="flex items-center gap-2">
                  <span className="text-green-400">✓</span>
                  Pacote de tokens para IAs (Gemini/GPT/Claude)
                </li>
              </ul>
              <button
                onClick={() => handleSubscription('price_1PjL8PGaOqxUlEi65sM2pYtP')}
                className="w-full py-2 px-4 rounded-lg bg-secondary/80 hover:bg-secondary transition-colors font-medium text-white"
              >
                Assinar
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
